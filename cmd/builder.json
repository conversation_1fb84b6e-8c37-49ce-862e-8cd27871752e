{"productName": "ee", "appId": "com.electron.ee", "copyright": "© 2025 duola Technology Co., Ltd.", "directories": {"output": "out"}, "asar": true, "files": ["**/*", "!cmd/", "!data/", "!electron/", "!frontend/", "!logs/", "!out/", "!go/", "!python/"], "extraResources": {"from": "build/extraResources/", "to": "extraResources"}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ee"}, "publish": [{"provider": "generic", "url": "https://github.com/wallace5303/electron-egg"}], "win": {"icon": "build/icons/icon.ico", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": [{"target": "nsis"}]}}