<template>
  <h2>设备号：{{ deviceCode }}</h2>
  <a-table :dataSource="list" :columns="columns">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <a-space>
          <a-button type="primary" @click="openWebview(record)">启动</a-button>
          <a-button type="primary" danger @click="closeWebview(record)"
            >销毁</a-button
          >
        </a-space>
      </template>
    </template>
  </a-table>
</template>
<script setup>
import axios from "axios";
import { generateUUID } from "@/utils/index";
import { ipcApiRoute } from "@/api";
import { ipc } from "@/utils/ipcRenderer";
import { onMounted, ref } from "vue";
import { message } from "ant-design-vue";

const deviceCode = ref(""); // 设备id
const list = ref([]);
const columns = [
  {
    title: "行情名称",
    dataIndex: "dataName",
    key: "dataName",
    width: 180,
  },
  {
    title: "行情key",
    dataIndex: "marketKey",
    key: "marketKey",
    width: 180,
  },
  {
    title: "行情链接",
    dataIndex: "tradingview",
    key: "tradingview",
    ellipsis: true,
  },
  {
    title: "状态",
    dataIndex: "state",
    key: "state",
    width: 100,
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 180,
  },
];
// 手动启动爬虫
const openWebview = (val) => {
  ipc.invoke(ipcApiRoute.webview.open, {
    key: val.marketKey,
    url: val.tradingview,
  }).then((item) => {
    if(item.key !== '' && item.key !== null){
      list.value.forEach((k) => {
        if(k.key === item.marketKey){
          k.state = 1
          k.winContentsId = item.winContentsId
        }
      })
    }
  });
};
// 关闭爬虫
const closeWebview = (val) => {
  ipc
    .invoke(ipcApiRoute.webview.close, {
      key: val.marketKey,
    })
    .then((res) => {
    });
};

const loginFun = (deviceCode) => {
  axios
    .get(import.meta.env.VITE_API_URL + "/system/dataList/all", {
      params: {
        deviceCode,
      },
    })
    .then((res) => {
      console.log(res);
      if (res.status === 200 && res.data.code === 200) {
        list.value = res.data.data;
      } else {
        message.error("获取数据失败");
      }
    });
};

onMounted(() => {
  console.log(import.meta.env.VITE_API_URL);
  ipc.invoke(ipcApiRoute.storage.getuid).then((res) => {
    console.log("设备号", res);
    if (!res) {
      const uuid = generateUUID();
      ipc
        .invoke(ipcApiRoute.storage.setuid, {
          deviceCode: uuid,
        })
        .then((res) => {
          deviceCode.value = uuid;
          loginFun(uuid);
        });
    } else {
      deviceCode.value = res.deviceCode;
      loginFun(res.deviceCode);
    }
  });
});
</script>
<style scoped>
section {
  padding: 42px 32px;
}

#hero {
  padding: 150px 32px;
  text-align: center;
  height: 100%;
}

.tagline {
  font-size: 52px;
  line-height: 1.25;
  font-weight: bold;
  letter-spacing: -1.5px;
  max-width: 960px;
  margin: 0px auto;
}
html:not(.dark) .accent,
.dark .tagline {
  background: -webkit-linear-gradient(315deg, #42d392 25%, #647eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  max-width: 960px;
  line-height: 1.5;
  color: var(--vt-c-text-2);
  transition: color 0.5s;
  font-size: 22px;
  margin: 24px auto 40px;
}
.actions a {
  font-size: 16px;
  display: inline-block;
  background-color: var(--vt-c-bg-mute);
  padding: 8px 18px;
  font-weight: 500;
  border-radius: 8px;
  transition: background-color 0.5s, color 0.5s;
  text-decoration: none;
}
.actions .setup {
  color: var(--vt-c-text-code);
  background: -webkit-linear-gradient(315deg, #42d392 25%, #647eff);
}
.actions .setup:hover {
  background-color: var(--vt-c-gray-light-4);
  transition-duration: 0.2s;
}
</style>
