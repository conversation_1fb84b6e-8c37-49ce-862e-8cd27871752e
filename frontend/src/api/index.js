import { getuid, setuid } from "process";

/**
 * 主进程与渲染进程通信频道定义
 * Definition of communication channels between main process and rendering process
 */
const ipcApiRoute = {
  example: {
    test: "controller/example/test",
  },
  storage: {
    getuid: "controller/storage/getuid",
    setuid: "controller/storage/setuid",
  },
  webview: {
    open: "controller/webview/open",
    getWindowId: "controller/webview/getWindowId",
    close: "controller/webview/close",
    getWindowId: "controller/webview/getWindowId",
    closeAll: "controller/webview/closeAll",
  },
};

export {
  ipcApiRoute
}

