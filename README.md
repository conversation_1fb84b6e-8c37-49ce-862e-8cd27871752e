[![star](https://gitee.com/dromara/electron-egg/badge/star.svg?theme=gvp)](https://gitee.com/dromara/electron-egg/stargazers)
[![GitHub](https://img.shields.io/github/stars/dromara/electron-egg.svg?style=social&label=Stars)](https://github.com/dromara/electron-egg)
[![Star](https://gitcode.com/dromara/electron-egg/star/badge.svg)](https://gitcode.com/dromara/electron-egg)
[![License](https://img.shields.io/badge/License-Apache-blue.svg)](https://gitee.com/dromara/electron-egg/blob/master/LICENSE)

<div align=center>
<h3>🎉🎉🎉 ElectronEgg v4 has been released! 🎉🎉🎉</h3>
</div>
<br>

<div align=center>
<img src="./public/images/example/logo.png" width="150" height="150" />
</div>

<div align=center>
<h3><strong>An easy to get started, cross platform, enterprise level desktop software development framework</strong></h3>
</div>
<br>

<!-- ## 🌏 [English](https://www.yuque.com/u34495/ee-doc) | [中文](https://www.kaka996.com/) -->

## 📋 Introduction

> The framework has been widely applied to the clients in various fields such as bookkeeping, government affairs, enterprises, healthcare, schools, stock trading, ERP, entertainment, and video. Please feel free to use it!

## 👦 Who can use it?

The project already has 5 communication groups, covering developers proficient in `frontend`, `Java`, `Go`, `Python`, `PHP`, etc.

Whether you're a frontend developer, backend developer, operations engineer, game developer, or focused on clientside development, you can get started quickly.

## 🐶 showcase

- [**Click to view**](#project-case)

## 📺 feature
- 🍩 **Why use...？** Desktop software (in the areas of office work and personal tools) will remain one of the demands on the PC side in the next decade or so, and it can improve work efficiency.
- 🍉 **simple：** support js、ts 
- 🍑 **vision：** All developers can learn to develop desktop software.
- 🍰 **gitee：** https://gitee.com/dromara/electron-egg **5600+**
- 🍨 **github：** https://github.com/dromara/electron-egg **2600+**
- 🍰 **gitcode：** https://gitcode.com/dromara/electron-egg 
- 🏆 The Most Valuable Open - source Projects on Gitee
    ![](./public/images/example/ee-zs.png) 
    ![](./public/images/example/ee-zs2.jpg)     

## 📚 document
- Quick experience：[Tutorial](https://www.kaka996.com/)
    ![](./public/images/example/v3-home.png) 

## 📦 characteristic
1. 🍄 Cross platform: One set of code can be packaged into Windows, Mac, Linux versions, as well as domestic versions like UOS, Deepin, and Kylin.
2. 🌹 Architecture: Single - business process / modular / multi - task (process, thread, rendering process), which simplifies the development of large - scale projects.
3. 🌱 Simple and efficient: Supports JavaScript (js) and TypeScript (ts).
4. 🌴 Independent frontend: Theoretically supports any frontend technology, such as Vue, React, HTML, etc.
5. 🍁 Engineering oriented: Desktop software can be developed using the development concepts of frontend and backend.
6. 🌷 High performance: Event driven, non blocking I/O.
7. 🌰 Rich in functions: Configuration, communication, plugins, database, upgrade, packaging, tools... everything is available.
8. 💐 Secure: Supports bytecode encryption, compression, and obfuscation encryption.
9. 🌻 Function demos: Common functions of desktop software, with the framework integrating or providing demos. 

## ✈️ use case

### 1. 🚀 conventional desktop software
- 🚖 windows

    ![](./public/images/example/ee-win-home.png)

- 🚍 macOS    
    ![](./public/images/example/ee-mac-home.png)

- 🚔 linux - UOS、Deepin
    ![](./public/images/example/uos-home.png)

- 🚔 linux - ubuntu
    ![](./public/images/example/ubuntu-db.png)

### 🚐 2. vue、react、angular、web, convert into desktop software
- 🚙 vue-ant-design（local）

    ![](./public/images/example/vue-antd.png)

- 🚙 zendao（web url）

    ![](./public/images/example/ee-project-7.png)

### 🚂 3. game（development related to H5 technologies）
- 🚊 Ninja 100 Floors

    ![](./public/images/example/ee_game_1.png)


## 📒 start using

- ✒️ [Installation document](https://www.kaka996.com/pages/e64ff6/)
    
## project-case
- 🐟 The framework has been applied to the clients in various fields, including healthcare, education, government affairs, stock trading, ERP, entertainment, video, and enterprises.

### 🐸 knowledge note

- [gaiyan](https://gaiyan.net?from=electron-egg) 
![](./public/images/example/gaiyan-1.jpg)
![](./public/images/example/gaiyan-2.png)

### 🐸 remote control

- RQ Center
![](./public/images/example/rq-1.png)
![](./public/images/example/rq-2.png)

### 🐸 cloud drive

- FM Cloud
![](./public/images/example/fm-p2.png)
![](./public/images/example/fm-p1.png)
![](./public/images/example/fm-p4.png)

### 🐸 IM

- Cede IM
![](./public/images/example/im-p1.png)
![](./public/images/example/im-p5.png)
![](./public/images/example/im-p1.png)

### 🐸 wallpaper

- warpar
![](./public/images/example/aw-3.png)

### 🐸 League of Legends Helper

- Serendlplty
![](./public/images/example/lol-zhanji.png)

### 🐸 more

- [More cases](https://www.kaka996.com/pages/eadf46/)

## 💬 communication
1. [discuss](https://www.kaka996.com/pages/c2720e/)

## 📌 about pr
Please go to the[GitHub project](https://github.com/dromara/electron-egg)to submit a PR（to avoid the PR being overwritten after the code is synchronized). Thank you!

Website address: https://github.com/dromara/electron-egg

## 📔 Framework core package: ee-core
ee-core：[https://github.com/wallace5303/ee-core](https://github.com/wallace5303/ee-core)

## 📚 Dromara member projects

<p align="center">
<a href="https://gitee.com/dromara/TLog" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/tlog2.png" title="一个轻量级的分布式日志标记追踪神器，10分钟即可接入，自动对日志打标签完成微服务的链路追踪" width="15%">
</a>
<a href="https://gitee.com/dromara/liteFlow" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/liteflow.png" title="轻量，快速，稳定，可编排的组件式流程引擎" width="15%">
</a>
<a href="https://hutool.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/hutool.jpg" title="小而全的Java工具类库，使Java拥有函数式语言般的优雅，让Java语言也可以“甜甜的”。" width="15%">
</a>
<a href="https://sa-token.dev33.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/sa-token.png" title="一个轻量级 java 权限认证框架，让鉴权变得简单、优雅！" width="15%">
</a>
<a href="https://gitee.com/dromara/hmily" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/hmily.png" title="高性能一站式分布式事务解决方案。" width="15%">
</a>
<a href="https://gitee.com/dromara/Raincat" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/raincat.png" title="强一致性分布式事务解决方案。" width="15%">
</a>
</p>
<p align="center">
<a href="https://gitee.com/dromara/myth" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/myth.png" title="可靠消息分布式事务解决方案。" width="15%">
</a>
<a href="https://cubic.jiagoujishu.com/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/cubic.png" title="一站式问题定位平台，以agent的方式无侵入接入应用，完整集成arthas功能模块，致力于应用级监控，帮助开发人员快速定位问题" width="15%">
</a>
<a href="https://maxkey.top/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/maxkey.png" title="业界领先的身份管理和认证产品" width="15%">
</a>
<a href="http://forest.dtflyx.com/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/forest-logo.png" title="Forest能够帮助您使用更简单的方式编写Java的HTTP客户端" width="15%">
</a>
<a href="https://jpom.io/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/jpom.png" title="一款简而轻的低侵入式在线构建、自动部署、日常运维、项目监控软件" width="15%">
</a>
<a href="https://su.usthe.com/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/sureness.png" title="面向 REST API 的高性能认证鉴权框架" width="15%">
</a>
</p>
<p align="center">
<a href="https://easy-es.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/easy-es2.png" title="傻瓜级ElasticSearch搜索引擎ORM框架" width="15%">
</a>
<a href="https://gitee.com/dromara/northstar" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/northstar_logo.png" title="Northstar盈富量化交易平台" width="15%">
</a>
<a href="https://hertzbeat.com/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/hertzbeat_brand.jpg" title="易用友好的云监控系统" width="15%">
</a>
<a href="https://plugins.sheng90.wang/fast-request/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/fast-request.gif" title="Idea 版 Postman，为简化调试API而生" width="15%">
</a>
<a href="https://www.jeesuite.com/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/mendmix.png" title="开源分布式云原生架构一站式解决方案" width="15%">
</a>
<a href="https://gitee.com/dromara/koalas-rpc" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/koalas-rpc2.png" title="企业生产级百亿日PV高可用可拓展的RPC框架。" width="15%">
</a>
</p>
<p align="center">
<a href="https://async.sizegang.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/gobrs-async.png" title="配置极简功能强大的异步任务动态编排框架" width="15%">
</a>
<a href="https://dynamictp.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/dynamic-tp.png" title="基于配置中心的轻量级动态可监控线程池" width="15%">
</a>
<a href="https://www.x-easypdf.cn" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/x-easypdf.png" title="一个用搭积木的方式构建pdf的框架（基于pdfbox）" width="15%">
</a>
<a href="http://dromara.gitee.io/image-combiner" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/image-combiner.png" title="一个专门用于图片合成的工具，没有很复杂的功能，简单实用，却不失强大" width="15%">
</a>
<a href="https://www.herodotus.cn/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/dante-cloud2.png" title="Dante-Cloud 是一款企业级微服务架构和服务能力开发平台。" width="15%">
</a>
<a href="https://dromara.org/zh/projects/" target="_blank">
<img src="https://oss.dev33.cn/sa-token/link/dromara.png" title="让每一位开源爱好者，体会到开源的快乐。" width="15%">
</a>
</p>