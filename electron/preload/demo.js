const btcNumber = () => {
  // 获取文本
  const text = document.getElementsByClassName("lastContainer-zoF9r75I")[0]
    .innerText;

  // 提取数字
  const numStr = text.replace(/[^0-9.]/g, "").replace(/,/g, "");

  // 如果需要 Number 类型
  const value = parseFloat(numStr);

  console.log(numStr); // 字符串形式 "114980.12"
};
window.onload = () => {
  if (document.getElementsByClassName("lastContainer-zoF9r75I")[0]) {
    btcNumber();
    setInterval(() => {
      btcNumber();
    }, 1000);
  }
};
