const elems = document.getElementsByClassName("lastContainer-zoF9r75I");

// 保存每个元素的上一次数值
const prevValues = new WeakMap();
let dotNumber = null;

Array.from(elems).forEach((elem) => {
  // 初始化记录
  prevValues.set(elem, elem.innerText);

  // 监听 DOM 内部变化
  const observer = new MutationObserver(() => {
    const oldVal = prevValues.get(elem);
    const newVal = elem.innerText;
    if (newVal !== oldVal) {
      console.log("变化的元素:", elem, "新数据:", newVal);
      dotNumber = newVal.replace(/[^0-9.]/g, "").replace(/,/g, "");
      prevValues.set(elem, newVal);
    } else {
      console.log("变化的元素:", elem, "新数据:", newVal);
      dotNumber = newVal.replace(/[^0-9.]/g, "").replace(/,/g, "");
    }
  });

  observer.observe(elem, {
    characterData: true, // 文本变化
    childList: true, // 子节点变化
    subtree: true, // 递归监听子节点
  });
});
setInterval(() => {
  console.log(dotNumber);
}, 1000);

// 老方法
let lastList = [];

const btcNumber = () => {
  const el = document.getElementsByClassName("lastContainer-zoF9r75I")[0];
  if (!el) return;

  // 获取文本并提取数字
  const text = el.innerText;
  const numStr = text.replace(/[^0-9.]/g, "").replace(/,/g, "");

  // 当前时间戳（秒）
  const now = Math.floor(Date.now() / 1000);

  // 保存到数组
  lastList.push({
    time: now,
    num: numStr,
  });

  // 保留最近 2 分钟
  lastList = lastList.filter((item) => now - item.time <= 1);
};

// 每秒采集一次
if (document.getElementsByClassName("lastContainer-zoF9r75I")[0]) {
  btcNumber();
  setInterval(btcNumber, 1000);
}

// 每 10 秒上传一次
setInterval(() => {
  if (lastList.length > 0) {
    const payload = {
      key: "btc",
      list: lastList,
    };
    console.log("上传数据:", payload);
    // ✅ 通过 sendMsg 发给主进程
    ipcRenderer.send("msg-from-renderer", payload);
  }
}, 1000);
