// preload.js
const { contextBridge, ipcRenderer } = require("electron");

contextBridge.exposeInMainWorld("wsInterceptor", {
  sendMsg: (msg) => ipcRenderer.send("msg-from-renderer", msg),
  onMsg: (callback) =>
    ipcRenderer.on("msg-from-main", (event, data) => callback(data)),
  init: () => {
    let lastList = [];

    const btcNumber = () => {
      const el = document.getElementsByClassName("lastContainer-zoF9r75I")[0];
      if (!el) return;

      // 获取文本并提取数字
      const text = el.innerText;
      const numStr = text.replace(/[^0-9.]/g, "").replace(/,/g, "");

      // 当前时间戳（秒）
      const now = Math.floor(Date.now() / 1000);

      // 保存到数组
      lastList.push({
        time: now,
        num: numStr,
      });

      // 保留最近 2 分钟
      lastList = lastList.filter((item) => now - item.time <= 120);
    };

    // 每秒采集一次
    if (document.getElementsByClassName("lastContainer-zoF9r75I")[0]) {
      btcNumber();
      setInterval(btcNumber, 1000);
    }

    // 每 10 秒上传一次
    setInterval(() => {
      if (lastList.length > 0) {
        const payload = {
          key: "btc",
          list: lastList,
        };
        console.log("上传数据:", payload);
        // ✅ 通过 sendMsg 发给主进程
        ipcRenderer.send("msg-from-renderer", payload);
      }
    }, 1000);
  },
});