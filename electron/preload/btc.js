// preload.js
const { contextBridge, ipc<PERSON><PERSON>er } = require("electron");

contextBridge.exposeInMainWorld("wsInterceptor", {
  sendMsg: (msg) => ipcRenderer.send("msg-from-renderer", msg),
  onMsg: (callback) =>
    ipcRenderer.on("msg-from-main", (event, data) => callback(data)),
  init: (key) => {
    const elems = document.getElementsByClassName("lastContainer-zoF9r75I");

    // 保存每个元素的上一次数值
    const prevValues = new WeakMap();
    let dotNumber = null;
    let lastList = [];

    Array.from(elems).forEach((elem) => {
      // 初始化记录
      prevValues.set(elem, elem.innerText);
      // 保证初始化有数据
      if (elem.innerText) {
        // 提取数字
        dotNumber = elem.innerText.replace(/[^0-9.]/g, "").replace(/,/g, "");
      }
      // 监听 DOM 内部变化
      const observer = new MutationObserver(() => {
        const oldVal = prevValues.get(elem);
        const newVal = elem.innerText;
        if (newVal !== oldVal) {
          // console.log("变化的元素:", elem, "新数据:", newVal);
          dotNumber = newVal.replace(/[^0-9.]/g, "").replace(/,/g, "");
          prevValues.set(elem, newVal);
        }
      });

      observer.observe(elem, {
        characterData: true, // 文本变化
        childList: true, // 子节点变化
        subtree: true, // 递归监听子节点
      });
    });

    // 每 1 秒上传一次
    setInterval(() => {
      if (dotNumber != null && dotNumber !== "") {
        // 当前时间戳（秒）
        const now = Math.floor(Date.now() / 1000);
        // 保存到数组
        lastList.push({
          time: now,
          num: dotNumber,
        });
        // 保留最近 5 秒
        lastList = lastList.filter((item) => now - item.time <= 5);

        if (lastList.length > 0) {
          const payload = {
            key,
            list: lastList,
          };
          console.log("✅ 通过 sendMsg 发给主进程");
          // ✅ 通过 sendMsg 发给主进程
          ipcRenderer.send("msg-from-renderer", payload);
        }
      }
    }, 1000);
  },
});
