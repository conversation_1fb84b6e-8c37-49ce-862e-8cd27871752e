"use strict";
const { app, BrowserWindow, ipcMain, session } = require("electron");
const axios = require("axios");
const path = require("path");

/**
 * framework
 * @class
 */
class WebviewService {
  constructor() {
    this.windowsNumber = {};
    // this.httpUrl = "http://*************:8082";
    this.httpUrl = "http://***************:8080";
  }
  /**
   * test
   */
  async webpage(args) {
    const { url, key } = args;
    const opt = {
      width: 1200,
      height: 800,
      webPreferences: {
        // session: customSession, // 👈 指定 session
        nodeIntegration: false, // 禁用 nodeIntegration
        contextIsolation: true, // 开启隔离环境
        preload: path.join(__dirname, "../preload", "btc.js"), // 用 preload 注入脚本
      },
    };

    const mainWindow = new BrowserWindow(opt);
    const winContentsId = mainWindow.webContents.id;

    // 加载目标网页
    mainWindow.loadURL(url);
    mainWindow.webContents.openDevTools();
    // 启用拦截
    mainWindow.webContents.once("did-finish-load", () => {
      mainWindow.webContents.executeJavaScript(
        `window.wsInterceptor.init("${key}");`
      );
    });
    this.windowsNumber[key] = {
      key,
      winContentsId,
      mainWindow,
    };
    // 监听渲染进程发来的消息
    ipcMain.on("msg-from-renderer", async (event, msg) => {
      console.log("收到渲染进程的消息:", msg);
      axios
        .post(this.httpUrl + "/api/common/stackData", msg)
        .then((response) => {
          console.log("上传成功" + response.data);
        })
        .catch((error) => {
          console.error("There was an error!", error);
        });
      // 回复给渲染进程
      // mainWindow.webContents.send("msg-from-main", "主进程已收到: " + msg);
    });
    return {
      key: key,
      winContentsId,
    };

    // ipcMain.on("ws-message", (event, { url, data }) => {
    //   console.log("[主进程] 拦截到 WebSocket 消息：", { url, data });
    //   // 这里可以进一步解析 JSON、转发、保存日志或推送给渲染进程
    // });

    // const { contextBridge, ipcRenderer } = require("electron");

    // // 将 API 暴露给渲染进程
    // contextBridge.exposeInMainWorld("electronAPI", {
    //   sendMsg: (msg) => ipcRenderer.send("msg-from-renderer", msg),
    //   onMsg: (callback) =>
    //     ipcRenderer.on("msg-from-main", (event, data) => callback(data)),
    // });
  }
  // 获取指定网页id
  getWindowId(key) {
    return this.windowsNumber[key];
  }
  // 关闭指定网页
  close(key) {
    const winObj = this.windowsNumber[key];
    if (winObj && winObj.mainWindow) {
      winObj.mainWindow.close();
      delete this.windowsNumber[key];
      console.log("destroyWindow done", this.windowsNumber[key]);
    } else {
      console.log("destroyWindow skipped, window already gone");
    }
  }
  // 关闭所有网页
  closeAll() {
    for (const key in this.windowsNumber) {
      const winObj = this.windowsNumber[key];
      if (winObj && winObj.mainWindow) {
        winObj.mainWindow.close();
        delete this.windowsNumber[key];
        console.log("destroyWindow done", this.windowsNumber[key]);
      } else {
        console.log("destroyWindow skipped, window already gone");
      }
    }
  }
}
WebviewService.toString = () => "[class WebviewService]";

module.exports = {
  WebviewService,
  webviewService: new WebviewService(),
};
