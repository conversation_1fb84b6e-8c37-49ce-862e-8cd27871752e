"use strict";

/**
 * example
 * @class
 */
class StorageController {
  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。详情见：控制器文档
   */
  async getuid() {
    const { default: Store } = await import("electron-store");
    const store = new Store();

    return store.get("deviceCode");
  }
  async setuid(args) {
    const { default: Store } = await import("electron-store");
    const store = new Store();

    store.set("deviceCode", args);
  }
}
StorageController.toString = () => "[class StorageController]";

module.exports = StorageController;
