"use strict";
const { webviewService } = require("../service/webview");

/**
 * effect - demo
 * @class
 */
class WebviewController {
  /**
   * 打开网页
   */
  open(args) {
    const wcid = webviewService.webpage(args);
    return wcid;
  }
  /**
   * 获取网页id
   */
  getWindowId(args) {
    const { key } = args;
    const wcid = webviewService.getWindowId(key);
    return wcid;
  }
  /**
   * 关闭网页
   */
  close(args) {
    const { key } = args;
    webviewService.close(key);
  }
  /**
   * 关闭所有网页
   */
  closeAll() {
    webviewService.closeAll();
  }
}
WebviewController.toString = () => "[class WebviewController]";

module.exports = WebviewController;
