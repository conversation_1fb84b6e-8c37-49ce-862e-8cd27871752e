{"name": "ee", "version": "4.1.0", "description": "A fast, desktop software development framework", "main": "./public/electron/main.js", "scripts": {"dev": "ee-bin dev", "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt", "start": "ee-bin start", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-electron": "ee-bin build --cmds=electron", "encrypt": "ee-bin encrypt", "icon": "ee-bin icon", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "ee-bin build --cmds=win64", "build-we": "ee-bin build --cmds=win_e", "build-m": "ee-bin build --cmds=mac", "build-m-arm64": "ee-bin build --cmds=mac_arm64", "build-l": "ee-bin build --cmds=linux"}, "repository": "https://github.com/dromara/electron-egg.git", "keywords": ["Electron", "electron-egg", "ElectronEgg"], "author": "哆啦好梦, Inc <<EMAIL>>", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.7.1", "@types/node": "^20.16.0", "debug": "^4.4.0", "ee-bin": "^4.1.10", "electron": "^31.7.7", "electron-builder": "^25.1.8"}, "dependencies": {"axios": "^1.11.0", "ee-core": "^4.1.5", "electron-store": "^10.1.0", "electron-updater": "^6.3.8"}, "packageManager": "pnpm@8.15.0+sha1.97e9ea18c26f67eaa5b0b394f1cfbcdc65b1b02d"}